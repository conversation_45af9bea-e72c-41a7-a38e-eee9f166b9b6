# Playwright Operation Logger 使用指南

## 快速开始

### 1. 系统要求

- Python 3.7+
- OpenManus 项目环境
- playwright-mcp 服务

### 2. 启用日志记录

```bash
# 进入项目目录
cd external/playwright-operation-logger

# 启用日志记录
python manage.py enable

# 查看状态
python manage.py status
```

### 3. 基本使用

一旦启用，系统会自动记录所有 playwright-mcp 操作。无需额外配置，日志文件会保存在 `logs/` 目录中。

## 管理命令

### 状态查看

```bash
# 查看系统状态
python manage.py status

# 输出示例：
# Playwright Operation Logger Status
# ========================================
# Enabled: True
# Log Directory: external/playwright-operation-logger/logs
# Session Prefix: agent_session
# Current Session: agent_session_1754037354689_gbecifij
# Total Operations: 5
# Successful: 4
# Failed: 1
```

### 日志管理

```bash
# 列出所有日志文件
python manage.py logs --list

# 查看特定日志文件
python manage.py logs --view session_id

# 清理旧日志文件（保留最近50个）
python manage.py logs --cleanup

# 合并多个日志文件
python manage.py logs --merge file1.json file2.json --output merged.json
```

### 配置管理

```bash
# 查看当前配置
python manage.py config --show

# 设置配置项
python manage.py config --set enabled=false
python manage.py config --set session_prefix=my_session

# 重置为默认配置
python manage.py config --reset
```

### 启用/禁用

```bash
# 启用日志记录
python manage.py enable

# 禁用日志记录
python manage.py disable
```

### 测试功能

```bash
# 运行测试套件
python manage.py test

# 输出示例：
# Testing Operation Logger...
# Test session saved to: external\playwright-operation-logger\logs\test_session_xxx.json
# Test file validation: PASSED
# Test completed successfully!
```

## 配置选项

### 环境变量配置

```bash
# 设置环境变量
export PLAYWRIGHT_LOGGER_ENABLED=true
export PLAYWRIGHT_LOGGER_LOG_DIR="custom/log/path"
export PLAYWRIGHT_LOGGER_PREFIX="custom_session"
export PLAYWRIGHT_LOGGER_ASYNC=true
```

### 配置文件

编辑 `config.json` 文件：

```json
{
  "enabled": true,
  "log_dir": "external/playwright-operation-logger/logs",
  "session_prefix": "agent_session",
  "excluded_tools": [
    "browser_screen_click",
    "browser_screen_drag",
    "browser_screen_hover",
    "browser_screen_key",
    "browser_screen_scroll",
    "browser_screen_type",
    "browser_install",
    "browser_snapshot"
  ],
  "include_visual_tools": false,
  "include_installation_tools": false,
  "async_logging": true,
  "max_log_size_mb": 100,
  "max_sessions_to_keep": 50,
  "extract_real_locators": true,
  "fallback_to_constructed_locators": true
}
```

#### 配置项说明

- `enabled`: 是否启用日志记录
- `log_dir`: 日志文件保存目录
- `session_prefix`: 会话ID前缀
- `excluded_tools`: 排除的工具列表
- `include_visual_tools`: 是否包含视觉模式工具
- `include_installation_tools`: 是否包含安装工具
- `async_logging`: 是否使用异步日志记录
- `max_log_size_mb`: 单个日志文件最大大小（MB）
- `max_sessions_to_keep`: 保留的最大会话数
- `extract_real_locators`: 是否提取真实定位器
- `fallback_to_constructed_locators`: 是否使用回退构造定位器

## 日志格式

### 会话结构

```json
{
  "session_id": "agent_session_1754037330820_bebgaabh",
  "start_time": 1754037330820,
  "records": [
    // 操作记录数组
  ],
  "metadata": {
    "total_operations": 5,
    "successful_operations": 4,
    "failed_operations": 1,
    "enhanced_logging": true
  }
}
```

### 操作记录结构

```json
{
  "step": 1,
  "success": true,
  "actions": [{
    "browser_click": {
      "ref": "e2",
      "role_selector": "role=button name=Submit",
      "element_description": "button \"Submit\""
    },
    "results": {
      "extracted_content": "Click successful",
      "include_in_memory": true,
      "execution_time": 500,
      "role_selector": "getByRole(\"button\", { name: \"Submit\" })"
    }
  }],
  "timestamp": 1754037330821,
  "duration": 500,
  "session_id": "agent_session_1754037330820_bebgaabh",
  "page_url": "https://example.com",
  "page_title": "Example Page",
  "original_action": "browser_click",
  "category": "element_interaction",
  "real_locators": {
    "role_selector": "getByRole(\"button\", { name: \"Submit\" })"
  }
}
```

## 编程接口

### 基本使用

```python
import sys
import os

# 添加模块路径
playwright_logger_path = os.path.join(
    os.path.dirname(__file__), 
    'external/playwright-operation-logger/src'
)
sys.path.insert(0, playwright_logger_path)

from operation_logger import OperationLogger

# 创建日志记录器
logger = OperationLogger(
    log_dir="logs",
    session_prefix="my_session"
)

# 记录操作
await logger.log_operation(
    tool_name="browser_click",
    tool_args={"ref": "e2", "element": "button \"Submit\""},
    result="Click successful",
    execution_time=500,
    success=True
)

# 保存会话
filepath = await logger.save_session()
print(f"Session saved to: {filepath}")
```

### 高级用法

```python
from operation_logger import OperationLogger
from config import LoggerConfig

# 自定义配置
config = LoggerConfig(
    enabled=True,
    log_dir="custom/logs",
    session_prefix="advanced_session",
    async_logging=True,
    extract_real_locators=True
)

# 使用自定义配置创建日志记录器
logger = OperationLogger.from_config(config)

# 批量记录操作
operations = [
    {
        "tool_name": "browser_navigate",
        "tool_args": {"url": "https://example.com"},
        "result": "Navigation successful",
        "execution_time": 1500,
        "success": True
    },
    {
        "tool_name": "browser_click",
        "tool_args": {"ref": "e2", "element": "button \"Submit\""},
        "result": "Click successful",
        "execution_time": 500,
        "success": True
    }
]

for op in operations:
    await logger.log_operation(**op)

# 等待所有异步操作完成
await logger.wait_for_pending_operations()

# 获取会话摘要
summary = logger.get_session_summary()
print(f"Session summary: {summary}")
```

## 故障排除

### 常见问题

1. **日志文件未生成**
   ```bash
   # 检查系统状态
   python manage.py status
   
   # 确保系统已启用
   python manage.py enable
   
   # 检查权限
   ls -la logs/
   ```

2. **定位器提取失败**
   ```bash
   # 检查配置
   python manage.py config --show
   
   # 确保启用了定位器提取
   python manage.py config --set extract_real_locators=true
   ```

3. **性能问题**
   ```bash
   # 启用异步日志记录
   python manage.py config --set async_logging=true
   
   # 减少日志文件大小限制
   python manage.py config --set max_log_size_mb=50
   ```

### 调试模式

```python
import logging

# 启用调试日志
logging.basicConfig(level=logging.DEBUG)

# 或者只启用操作日志记录器的调试
logger = logging.getLogger('operation_logger')
logger.setLevel(logging.DEBUG)
```

### 日志验证

```bash
# 验证日志文件格式
python -c "
import json
with open('logs/session_file.json', 'r') as f:
    data = json.load(f)
    print('Valid JSON format')
    print(f'Records: {len(data[\"records\"])}')
"
```

## 最佳实践

1. **定期清理日志**
   ```bash
   # 设置定时任务清理旧日志
   python manage.py logs --cleanup
   ```

2. **监控日志大小**
   ```bash
   # 检查日志目录大小
   du -sh logs/
   ```

3. **备份重要会话**
   ```bash
   # 备份特定会话
   cp logs/important_session.json backup/
   ```

4. **性能优化**
   - 启用异步日志记录
   - 设置合理的文件大小限制
   - 定期清理旧文件

5. **安全考虑**
   - 不要在日志中记录敏感信息
   - 设置适当的文件权限
   - 定期审查日志内容

## 集成示例

### 与现有代码集成

```python
# 在 MCP 工具执行中集成
class MCPClientTool:
    def __init__(self):
        # 初始化操作日志记录器
        self.operation_logger = None
        self._init_operation_logger()
    
    def _init_operation_logger(self):
        try:
            from operation_logger import OperationLogger
            self.operation_logger = OperationLogger()
        except ImportError:
            # 优雅降级
            self.operation_logger = None
    
    async def execute(self, tool_name: str, arguments: dict):
        start_time = time.time()
        success = True
        result = None
        
        try:
            result = await self._execute_tool(tool_name, arguments)
            return result
        except Exception as e:
            success = False
            result = str(e)
            raise
        finally:
            # 记录操作
            if self.operation_logger:
                execution_time = (time.time() - start_time) * 1000
                await self.operation_logger.log_operation(
                    tool_name, arguments, result, execution_time, success
                )
```

这个使用指南提供了完整的操作说明，帮助用户快速上手并有效使用 Playwright Operation Logger 系统。
