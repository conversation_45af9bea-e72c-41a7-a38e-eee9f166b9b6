"""
Locator Extractor - Extract and convert element locators from MCP tool calls
"""

import logging
import re
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class LocatorExtractor:
    """
    Extract real Playwright locators from MCP tool arguments and results.

    This class handles the conversion from ref-based selectors (like ref=e2)
    to actual Playwright locator syntax (like getByRole('button', { name: 'Submit' })).
    """

    def __init__(self):
        # Pattern to extract Playwright code from result content
        self.playwright_code_pattern = re.compile(
            r"```js\n(.*?)\n```", re.DOTALL | re.MULTILINE
        )

        # Pattern to extract locator calls
        self.locator_pattern = re.compile(
            r"await page\.(.*?)\.(click|fill|selectOption|screenshot|press|pressSequentially|dragTo)\(",
            re.MULTILINE,
        )

        # Pattern to extract getByRole calls (support both single and double quotes)
        self.get_by_role_pattern = re.compile(
            r'getByRole\(["\']([^"\']+)["\'](?:,\s*\{\s*name:\s*["\']([^"\']+)["\']\s*\})?\)',
            re.MULTILINE,
        )

        # Pattern to extract other getBy calls (support both single and double quotes)
        self.get_by_pattern = re.compile(
            r'(getBy\w+)\(["\']([^"\']+)["\']\)', re.MULTILINE
        )

    async def extract_locators(
        self, tool_name: str, tool_args: Dict[str, Any], result: Any
    ) -> Optional[Dict[str, str]]:
        """
        Extract real locators from tool execution.

        Args:
            tool_name: Name of the MCP tool
            tool_args: Arguments passed to the tool
            result: Tool execution result

        Returns:
            Dictionary with locator information or None
        """
        try:
            # Extract from result content if available
            result_locator = self._extract_from_result(result)
            if result_locator:
                return {"role_selector": result_locator}

            # Fallback: construct from tool arguments
            constructed_locator = self._construct_from_args(tool_name, tool_args)
            if constructed_locator:
                return {"role_selector": constructed_locator}

            return None

        except Exception as e:
            logger.error(f"Error extracting locators for {tool_name}: {e}")
            return None

    def _extract_from_result(self, result: Any) -> Optional[str]:
        """Extract locator from tool execution result"""
        try:
            # Get result content as string
            result_str = str(result)

            # Look for Playwright code blocks
            code_matches = self.playwright_code_pattern.findall(result_str)
            if not code_matches:
                return None

            # Process each code block
            for code_block in code_matches:
                # Extract locator calls
                locator_matches = self.locator_pattern.findall(code_block)
                if locator_matches:
                    locator_part = locator_matches[0][0]  # Get the locator part
                    return self._format_locator_string(locator_part)

            return None

        except Exception as e:
            logger.error(f"Error extracting from result: {e}")
            return None

    def _format_locator_string(self, locator_part: str) -> str:
        """Format locator part into Python Playwright syntax"""
        try:
            # Handle getByRole calls - convert to Python format
            role_matches = self.get_by_role_pattern.findall(locator_part)
            if role_matches:
                role, name = role_matches[0]
                if name:
                    return f'get_by_role("{role}", name="{name}")'
                else:
                    return f'get_by_role("{role}")'

            # Handle other getBy calls - convert to Python format
            get_by_matches = self.get_by_pattern.findall(locator_part)
            if get_by_matches:
                method, value = get_by_matches[0]
                # Convert camelCase to snake_case
                python_method = self._camel_to_snake(method)
                return f'{python_method}("{value}")'

            # Return as-is if no specific pattern matched
            return locator_part

        except Exception as e:
            logger.error(f"Error formatting locator: {e}")
            return locator_part

    def _camel_to_snake(self, camel_str: str) -> str:
        """Convert camelCase to snake_case"""
        # Insert underscore before uppercase letters and convert to lowercase
        import re

        snake_str = re.sub(r"(?<!^)(?=[A-Z])", "_", camel_str).lower()
        return snake_str

    def _construct_from_args(
        self, tool_name: str, tool_args: Dict[str, Any]
    ) -> Optional[str]:
        """Construct locator from tool arguments as fallback"""
        try:
            # For tools with element description, try to construct a reasonable locator
            if "element" in tool_args:
                element_desc = tool_args["element"]

                # Try to infer locator type from element description
                if "button" in element_desc.lower():
                    # Extract button name if possible
                    name = self._extract_element_name(element_desc)
                    if name:
                        return f'getByRole("button", {{ name: "{name}" }})'
                    else:
                        return 'getByRole("button")'

                elif (
                    "textbox" in element_desc.lower() or "input" in element_desc.lower()
                ):
                    name = self._extract_element_name(element_desc)
                    if name:
                        return f'getByRole("textbox", {{ name: "{name}" }})'
                    else:
                        return 'getByRole("textbox")'

                elif "link" in element_desc.lower():
                    name = self._extract_element_name(element_desc)
                    if name:
                        return f'getByRole("link", {{ name: "{name}" }})'
                    else:
                        return 'getByRole("link")'

                elif "checkbox" in element_desc.lower():
                    name = self._extract_element_name(element_desc)
                    if name:
                        return f'getByRole("checkbox", {{ name: "{name}" }})'
                    else:
                        return 'getByRole("checkbox")'

                elif (
                    "combobox" in element_desc.lower()
                    or "select" in element_desc.lower()
                ):
                    return 'getByRole("combobox")'

                else:
                    # Generic text-based locator
                    return f'getByText("{element_desc}")'

            return None

        except Exception as e:
            logger.error(f"Error constructing locator from args: {e}")
            return None

    def _extract_element_name(self, element_desc: str) -> Optional[str]:
        """Extract element name from description"""
        try:
            # Look for quoted text
            quote_pattern = re.compile(r'"([^"]+)"')
            matches = quote_pattern.findall(element_desc)
            if matches:
                return matches[0]

            # Look for text after common prefixes
            prefixes = ["button", "textbox", "link", "checkbox"]
            for prefix in prefixes:
                if prefix in element_desc.lower():
                    # Try to extract text after the prefix
                    pattern = re.compile(f'{prefix}\\s+"([^"]+)"', re.IGNORECASE)
                    matches = pattern.findall(element_desc)
                    if matches:
                        return matches[0]

            return None

        except Exception as e:
            logger.error(f"Error extracting element name: {e}")
            return None

    def get_locator_info(
        self, tool_name: str, tool_args: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get comprehensive locator information for logging"""
        info = {
            "tool_name": tool_name,
            "has_ref": "ref" in tool_args,
            "has_element": "element" in tool_args,
            "ref_value": tool_args.get("ref"),
            "element_description": tool_args.get("element"),
        }

        # Add tool-specific information
        if tool_name == "browser_type":
            info["text_input"] = tool_args.get("text")
        elif tool_name == "browser_select_option":
            info["selected_values"] = tool_args.get("values")
        elif tool_name == "browser_navigate":
            info["target_url"] = tool_args.get("url")

        return info
