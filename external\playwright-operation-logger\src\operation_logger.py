"""
Playwright Operation Logger - Core logging functionality
"""

import asyncio
import json
import logging
import time
import uuid
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    from .locator_extractor import LocatorExtractor
    from .log_formatter import Log<PERSON><PERSON>atter
except ImportError:
    from locator_extractor import LocatorExtractor
    from log_formatter import LogFormatter

logger = logging.getLogger(__name__)


@dataclass
class OperationRecord:
    """Single operation record"""

    step: int
    success: bool
    actions: List[Dict[str, Any]]
    timestamp: int
    duration: int
    session_id: str
    page_url: Optional[str] = None
    page_title: Optional[str] = None
    original_action: Optional[str] = None
    category: Optional[str] = None
    real_locators: Optional[Dict[str, str]] = None


@dataclass
class SessionMetadata:
    """Session metadata"""

    total_operations: int = 0
    successful_operations: int = 0
    failed_operations: int = 0
    enhanced_logging: bool = True


class OperationLogger:
    """
    Core operation logger for Playwright MCP operations.

    Features:
    - Asynchronous logging to avoid blocking main operations
    - Real locator extraction from ref-based selectors
    - Configurable filtering of operations
    - JSON format compatible with existing logs
    """

    def __init__(
        self,
        log_dir: str = "external/playwright-operation-logger/logs",
        enabled: bool = True,
        session_prefix: str = "agent_session",
    ):
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(parents=True, exist_ok=True)
        self.enabled = enabled
        self.session_prefix = session_prefix

        # Current session data
        self.session_id = self._generate_session_id()
        self.session_start_time = int(time.time() * 1000)
        self.operation_records: List[OperationRecord] = []
        self.step_counter = 0
        self.metadata = SessionMetadata()

        # Components
        self.formatter = LogFormatter()
        self.locator_extractor = LocatorExtractor()

        # Excluded tools (visual mode and installation tools)
        self.excluded_tools = {
            "browser_screen_click",
            "browser_screen_drag",
            "browser_screen_hover",
            "browser_screen_key",
            "browser_screen_scroll",
            "browser_screen_type",
            "browser_snapshot",
            "browser_install",
        }

        # For compound operation detection
        self._last_operation_time = 0
        self._compound_operation_window = (
            5000  # 5 seconds window for compound operations
        )
        self._compound_operation_patterns = {
            # Pattern: (first_action, second_action) -> combined_category
            ("browser_type", "browser_press_key"): "search_operation",
            ("browser_type", "browser_click"): "form_submission",
            ("browser_click", "browser_type"): "form_interaction",
        }

        logger.info(f"OperationLogger initialized with session_id: {self.session_id}")

    def _generate_session_id(self) -> str:
        """Generate unique session ID"""
        timestamp = int(time.time() * 1000)
        random_suffix = "".join(
            [chr(ord("a") + (int(c) % 26)) for c in str(uuid.uuid4().int)[:8]]
        )
        return f"{self.session_prefix}_{timestamp}_{random_suffix}"

    def _should_log_operation(self, tool_name: str) -> bool:
        """Check if operation should be logged"""
        if not self.enabled:
            return False
        return tool_name not in self.excluded_tools

    async def log_operation(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        result: Any,
        execution_time: float,
        success: bool = True,
        page_info: Optional[Dict[str, str]] = None,
    ) -> None:
        """
        Log a single operation asynchronously.

        Args:
            tool_name: Name of the MCP tool
            tool_args: Arguments passed to the tool
            result: Tool execution result
            execution_time: Execution time in milliseconds
            success: Whether operation was successful
            page_info: Optional page URL and title info
        """
        if not self._should_log_operation(tool_name):
            return

        try:
            # Run in background to avoid blocking
            task = asyncio.create_task(
                self._log_operation_async(
                    tool_name, tool_args, result, execution_time, success, page_info
                )
            )
            # Store task reference to prevent garbage collection
            if not hasattr(self, "_pending_tasks"):
                self._pending_tasks = set()
            self._pending_tasks.add(task)
            task.add_done_callback(self._pending_tasks.discard)
        except Exception as e:
            logger.error(f"Failed to create logging task: {e}")

    async def _log_operation_async(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        result: Any,
        execution_time: float,
        success: bool,
        page_info: Optional[Dict[str, str]],
    ) -> None:
        """Internal async logging implementation"""
        try:
            current_time = int(time.time() * 1000)

            # Check for compound operations
            compound_detected = self._detect_compound_operation(tool_name, current_time)

            if compound_detected:
                # Merge with previous operation instead of creating new one
                await self._merge_with_previous_operation(
                    tool_name, tool_args, result, execution_time, success, page_info
                )
                return

            self.step_counter += 1

            # Extract real locators if applicable
            real_locators = await self.locator_extractor.extract_locators(
                tool_name, tool_args, result
            )

            # Format the action data
            action_data = self.formatter.format_action(
                tool_name, tool_args, result, real_locators
            )

            # Create operation record
            record = OperationRecord(
                step=self.step_counter,
                success=success,
                actions=[action_data],
                timestamp=int(time.time() * 1000),
                duration=int(execution_time),
                session_id=self.session_id,
                page_url=page_info.get("url") if page_info else None,
                page_title=page_info.get("title") if page_info else None,
                original_action=tool_name,
                category=self._categorize_operation(tool_name),
                real_locators=real_locators,
            )

            self.operation_records.append(record)

            # Update metadata
            if success:
                self.metadata.successful_operations += 1
            else:
                self.metadata.failed_operations += 1
            self.metadata.total_operations += 1

            # Update last operation time for compound detection
            self._last_operation_time = current_time

            logger.debug(f"Logged operation: {tool_name} (step {self.step_counter})")

        except Exception as e:
            logger.error(f"Error in async logging: {e}", exc_info=True)

    def _categorize_operation(self, tool_name: str) -> str:
        """Categorize operation type"""
        if tool_name.startswith("browser_tab"):
            return "tab_management"
        elif tool_name in ["browser_click", "browser_type", "browser_select_option"]:
            return "element_interaction"
        elif tool_name in ["browser_navigate"]:
            return "navigation"
        elif tool_name.startswith("browser_wait"):
            return "waiting"
        elif tool_name.startswith("browser_file"):
            return "file_operation"
        else:
            return "other"

    def _detect_compound_operation(self, tool_name: str, current_time: int) -> bool:
        """Detect if current operation is part of a compound operation"""
        if not self.operation_records:
            return False

        # Check if within time window
        time_diff = current_time - self._last_operation_time
        if time_diff > self._compound_operation_window:
            return False

        # Get last operation
        last_record = self.operation_records[-1]
        last_action = last_record.original_action

        # Check if pattern matches
        pattern = (last_action, tool_name)
        return pattern in self._compound_operation_patterns

    async def _merge_with_previous_operation(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        result: Any,
        execution_time: float,
        success: bool,
        page_info: Optional[Dict[str, str]],
    ) -> None:
        """Merge current operation with previous operation"""
        try:
            if not self.operation_records:
                return

            # Get the last record
            last_record = self.operation_records[-1]

            # Extract real locators for current operation
            real_locators = await self.locator_extractor.extract_locators(
                tool_name, tool_args, result
            )

            # Format the current action data
            action_data = self.formatter.format_action(
                tool_name, tool_args, result, real_locators
            )

            # Add current action to the previous record
            last_record.actions.append(action_data)

            # Update the record's category based on compound pattern
            last_action = last_record.original_action
            pattern = (last_action, tool_name)
            if pattern in self._compound_operation_patterns:
                last_record.category = self._compound_operation_patterns[pattern]

            # Update duration (extend to include current operation)
            current_time = int(time.time() * 1000)
            last_record.duration = current_time - last_record.timestamp

            # Update success status (compound operation fails if any part fails)
            if not success:
                last_record.success = False

            logger.debug(f"Merged {tool_name} with previous {last_action} operation")

        except Exception as e:
            logger.error(f"Error merging compound operation: {e}")

    async def wait_for_pending_operations(self) -> None:
        """Wait for all pending async operations to complete"""
        if hasattr(self, "_pending_tasks") and self._pending_tasks:
            await asyncio.gather(*self._pending_tasks, return_exceptions=True)

    async def save_session(self) -> str:
        """Save current session to JSON file"""
        # Wait for any pending operations to complete
        await self.wait_for_pending_operations()

        if not self.operation_records:
            logger.info("No operations to save")
            return ""

        try:
            # Prepare session data
            session_data = {
                "session_id": self.session_id,
                "start_time": self.session_start_time,
                "records": [asdict(record) for record in self.operation_records],
                "metadata": asdict(self.metadata),
            }

            # Generate filename
            filename = f"{self.session_id}.json"
            filepath = self.log_dir / filename

            # Save to file
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Session saved to: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"Failed to save session: {e}", exc_info=True)
            return ""

    def get_session_summary(self) -> Dict[str, Any]:
        """Get current session summary"""
        return {
            "session_id": self.session_id,
            "total_operations": len(self.operation_records),
            "successful_operations": self.metadata.successful_operations,
            "failed_operations": self.metadata.failed_operations,
            "start_time": self.session_start_time,
            "enabled": self.enabled,
        }
