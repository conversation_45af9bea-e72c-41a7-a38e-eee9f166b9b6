# Playwright Operation Logger 实现文档

## 概述

本文档详细描述了为 OpenManus 项目实现的 Playwright 操作日志记录系统。该系统能够记录代理调用 playwright-mcp 服务时的每一个操作步骤，捕获浏览器操作（排除视觉模式工具），记录元素定位器，并将 playwright-mcp 的引用式元素标记转换为真实的 Playwright 定位器语法。

## 系统架构

### 核心组件

1. **OperationLogger** (`operation_logger.py`)
   - 核心日志记录器，负责会话管理和操作记录
   - 支持异步日志记录，避免阻塞主要自动化流程
   - 管理步骤计数、元数据跟踪和会话保存

2. **LocatorExtractor** (`locator_extractor.py`)
   - 从 MCP 工具参数和结果中提取真实的 Playwright 定位器
   - 处理从基于引用的选择器到实际 Playwright 语法的转换
   - 支持多种定位器类型：role、text、placeholder、label 等

3. **LogFormatter** (`log_formatter.py`)
   - 将操作数据格式化为 JSON 日志结构
   - 处理不同操作类型的特定格式化
   - 确保与现有项目日志格式兼容

4. **MCPToolInterceptor** (`mcp_interceptor.py`)
   - 为 MCP 工具调用提供拦截功能
   - 管理全局拦截器实例和补丁机制
   - 创建日志记录钩子函数

5. **Configuration** (`config.py`)
   - 配置管理，支持环境变量
   - 定义排除工具、性能设置和功能开关
   - 提供灵活的配置选项

6. **Utilities** (`utils.py`)
   - 日志管理和验证的实用函数
   - 文件管理和维护功能
   - 日志清理和合并工具

### 集成点

系统通过修改 `app/tool/mcp.py` 中的 `MCPClientTool.execute()` 方法集成到现有的 MCP 工具执行流程中。

## 技术实现细节

### 1. 异步日志记录

```python
async def log_operation(self, tool_name: str, tool_args: Dict[str, Any], 
                       result: Any, execution_time: float, success: bool = True):
    """异步记录操作，避免阻塞主流程"""
    if not self._should_log_operation(tool_name):
        return
    
    task = asyncio.create_task(
        self._log_operation_async(tool_name, tool_args, result, execution_time, success)
    )
    # 存储任务引用防止垃圾回收
    self._pending_tasks.add(task)
    task.add_done_callback(self._pending_tasks.discard)
```

### 2. 定位器提取

系统使用正则表达式和回退构造来提取真实的 Playwright 定位器：

```python
async def extract_locators(self, tool_name: str, tool_args: Dict[str, Any], 
                          result: Any) -> Optional[Dict[str, str]]:
    """从工具结果中提取定位器"""
    # 从结果中提取 JavaScript 代码块
    js_pattern = r'```(?:js|javascript)\n(.*?)\n```'
    matches = re.findall(js_pattern, str(result), re.DOTALL | re.IGNORECASE)
    
    if matches:
        # 提取 getByRole, getByText 等调用
        locator_patterns = [
            r'\.getByRole\([^)]+\)',
            r'\.getByText\([^)]+\)',
            r'\.getByPlaceholder\([^)]+\)',
            # ... 更多模式
        ]
```

### 3. 日志格式化

日志采用与现有项目兼容的 JSON 格式：

```json
{
  "session_id": "agent_session_1754037330820_bebgaabh",
  "start_time": 1754037330820,
  "records": [
    {
      "step": 1,
      "success": true,
      "actions": [{
        "browser_click": {
          "ref": "e2",
          "role_selector": "role=button name=Submit",
          "element_description": "button \"Submit\""
        },
        "results": {
          "extracted_content": "Click successful",
          "include_in_memory": true,
          "execution_time": 500,
          "role_selector": "getByRole(\"button\", { name: \"Submit\" })"
        }
      }],
      "real_locators": {
        "role_selector": "getByRole(\"button\", { name: \"Submit\" })"
      }
    }
  ]
}
```

## 代码改动

### 主要修改文件

1. **app/tool/mcp.py**
   - 添加了操作日志记录的导入和初始化
   - 修改 `execute()` 方法以包含日志记录钩子
   - 添加了日志管理方法：
     - `save_operation_logs()`
     - `get_operation_log_summary()`
     - `enable_operation_logging()`
     - `disable_operation_logging()`

### 新增文件结构

```
external/playwright-operation-logger/
├── src/
│   ├── operation_logger.py      # 核心日志记录器
│   ├── locator_extractor.py     # 定位器提取器
│   ├── log_formatter.py         # 日志格式化器
│   ├── mcp_interceptor.py       # MCP 拦截器
│   ├── config.py                # 配置管理
│   └── utils.py                 # 实用工具
├── logs/                        # 日志文件目录
├── config.json                  # 默认配置文件
├── manage.py                    # 命令行管理脚本
└── test_logger.py              # 测试脚本
```

## 集成方式

### 1. 导入路径处理

由于模块位于 `external` 目录，使用动态路径添加：

```python
# 在 app/tool/mcp.py 中
playwright_logger_path = os.path.join(os.path.dirname(__file__), 
                                     '../../external/playwright-operation-logger/src')
if os.path.exists(playwright_logger_path):
    sys.path.insert(0, playwright_logger_path)
    from operation_logger import OperationLogger
```

### 2. 钩子集成

在 MCP 工具执行过程中添加日志记录钩子：

```python
async def execute(self, tool_name: str, arguments: dict) -> Any:
    start_time = time.time()
    success = True
    result = None
    
    try:
        # 执行原有逻辑
        result = await self._execute_tool(tool_name, arguments)
        return result
    except Exception as e:
        success = False
        result = str(e)
        raise
    finally:
        # 记录操作
        if hasattr(self, 'operation_logger') and self.operation_logger:
            execution_time = (time.time() - start_time) * 1000
            await self.operation_logger.log_operation(
                tool_name, arguments, result, execution_time, success
            )
```

## 配置选项

### 环境变量支持

- `PLAYWRIGHT_LOGGER_ENABLED`: 启用/禁用日志记录
- `PLAYWRIGHT_LOGGER_LOG_DIR`: 日志目录路径
- `PLAYWRIGHT_LOGGER_PREFIX`: 会话前缀
- `PLAYWRIGHT_LOGGER_ASYNC`: 异步日志记录开关

### 配置文件选项

```json
{
  "enabled": true,
  "log_dir": "external/playwright-operation-logger/logs",
  "session_prefix": "agent_session",
  "excluded_tools": [
    "browser_screen_click",
    "browser_screen_drag",
    "browser_screen_hover",
    "browser_screen_key",
    "browser_screen_scroll",
    "browser_screen_type",
    "browser_install",
    "browser_snapshot"
  ],
  "async_logging": true,
  "max_log_size_mb": 100,
  "extract_real_locators": true
}
```

## 使用方法

### 命令行管理

```bash
# 查看状态
python manage.py status

# 启用/禁用日志记录
python manage.py enable
python manage.py disable

# 查看日志文件
python manage.py logs --list

# 运行测试
python manage.py test

# 清理旧日志
python manage.py logs --cleanup
```

### 编程接口

```python
from operation_logger import OperationLogger

# 创建日志记录器
logger = OperationLogger()

# 记录操作
await logger.log_operation(
    tool_name="browser_click",
    tool_args={"ref": "e2", "element": "button \"Submit\""},
    result="Click successful",
    execution_time=500,
    success=True
)

# 保存会话
filepath = await logger.save_session()
```

## 性能考虑

1. **异步处理**: 所有日志记录操作都是异步的，不会阻塞主要的自动化流程
2. **任务管理**: 使用任务引用防止垃圾回收，确保异步任务完成
3. **文件大小限制**: 配置最大日志文件大小，防止磁盘空间耗尽
4. **会话数量限制**: 自动清理旧的日志文件，保持系统整洁

## 错误处理

系统包含全面的错误处理机制：

1. **导入错误**: 如果模块不可用，系统会优雅地降级
2. **日志记录错误**: 日志记录失败不会影响主要功能
3. **文件系统错误**: 处理磁盘空间不足、权限问题等
4. **配置错误**: 提供默认配置作为回退

## 测试验证

系统包含完整的测试套件：

1. **单元测试**: 测试各个组件的功能
2. **集成测试**: 验证组件间的协作
3. **性能测试**: 确保异步操作正常工作
4. **格式验证**: 验证生成的日志格式正确性

测试结果显示系统能够：
- 正确记录各种类型的操作
- 准确提取 Playwright 定位器
- 生成符合格式要求的 JSON 日志
- 在异步环境中稳定工作

## 总结

Playwright Operation Logger 系统成功实现了以下目标：

1. ✅ 记录每个 playwright-mcp 操作步骤
2. ✅ 捕获浏览器操作（排除视觉模式工具）
3. ✅ 记录元素定位器信息
4. ✅ 转换引用式标记为真实 Playwright 定位器语法
5. ✅ 保存为 JSON 格式日志
6. ✅ 提供管理和配置工具
7. ✅ 确保系统性能和稳定性

该系统为 OpenManus 项目提供了强大的操作追踪和调试能力，同时保持了与现有系统的兼容性和非侵入性设计。
