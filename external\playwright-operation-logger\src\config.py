"""
Configuration for Playwright Operation Logger
"""

import os
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class LoggerConfig:
    """Configuration for the operation logger"""
    
    # Basic settings
    enabled: bool = True
    log_dir: str = "external/playwright-operation-logger/logs"
    session_prefix: str = "agent_session"
    
    # Filtering settings
    excluded_tools: List[str] = None
    include_visual_tools: bool = False
    include_installation_tools: bool = False
    
    # Performance settings
    async_logging: bool = True
    max_log_size_mb: int = 100
    max_sessions_to_keep: int = 50
    
    # Locator extraction settings
    extract_real_locators: bool = True
    fallback_to_constructed_locators: bool = True
    
    def __post_init__(self):
        """Initialize default excluded tools if not provided"""
        if self.excluded_tools is None:
            self.excluded_tools = []
            
            # Add visual mode tools if not included
            if not self.include_visual_tools:
                self.excluded_tools.extend([
                    'browser_screen_click',
                    'browser_screen_drag', 
                    'browser_screen_hover',
                    'browser_screen_key',
                    'browser_screen_scroll',
                    'browser_screen_type'
                ])
            
            # Add installation and snapshot tools if not included
            if not self.include_installation_tools:
                self.excluded_tools.extend([
                    'browser_install',
                    'browser_snapshot'
                ])
    
    @classmethod
    def from_env(cls) -> 'LoggerConfig':
        """Create configuration from environment variables"""
        return cls(
            enabled=os.getenv('PLAYWRIGHT_LOGGER_ENABLED', 'true').lower() == 'true',
            log_dir=os.getenv('PLAYWRIGHT_LOGGER_DIR', 'external/playwright-operation-logger/logs'),
            session_prefix=os.getenv('PLAYWRIGHT_LOGGER_PREFIX', 'agent_session'),
            include_visual_tools=os.getenv('PLAYWRIGHT_LOGGER_VISUAL', 'false').lower() == 'true',
            include_installation_tools=os.getenv('PLAYWRIGHT_LOGGER_INSTALL', 'false').lower() == 'true',
            async_logging=os.getenv('PLAYWRIGHT_LOGGER_ASYNC', 'true').lower() == 'true',
            extract_real_locators=os.getenv('PLAYWRIGHT_LOGGER_EXTRACT', 'true').lower() == 'true',
            fallback_to_constructed_locators=os.getenv('PLAYWRIGHT_LOGGER_FALLBACK', 'true').lower() == 'true'
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'LoggerConfig':
        """Create configuration from dictionary"""
        return cls(**config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'enabled': self.enabled,
            'log_dir': self.log_dir,
            'session_prefix': self.session_prefix,
            'excluded_tools': self.excluded_tools,
            'include_visual_tools': self.include_visual_tools,
            'include_installation_tools': self.include_installation_tools,
            'async_logging': self.async_logging,
            'max_log_size_mb': self.max_log_size_mb,
            'max_sessions_to_keep': self.max_sessions_to_keep,
            'extract_real_locators': self.extract_real_locators,
            'fallback_to_constructed_locators': self.fallback_to_constructed_locators
        }


# Default configuration instance
DEFAULT_CONFIG = LoggerConfig()


def get_config() -> LoggerConfig:
    """Get the current configuration (from environment by default)"""
    return LoggerConfig.from_env()


def create_config(**kwargs) -> LoggerConfig:
    """Create a custom configuration"""
    return LoggerConfig(**kwargs)
