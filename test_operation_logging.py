#!/usr/bin/env python
"""
测试操作日志记录系统
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def test_operation_logging():
    """测试操作日志记录系统"""
    try:
        print("🚀 开始测试操作日志记录系统...")
        
        # 创建 Manus 实例
        agent = await Manus.create()
        print("✅ Manus 实例创建成功")

        # 检查连接状态
        if agent.mcp_clients.sessions:
            available_tools = list(agent.mcp_clients.tool_map.keys())
            browser_tools = [tool for tool in available_tools if "browser" in tool.lower()]
            
            print(f"✅ 成功连接到 MCP 服务，发现 {len(browser_tools)} 个浏览器工具")
            
            if browser_tools:
                # 选择一个简单的工具进行测试
                test_tool_name = None
                for tool_name in browser_tools:
                    if "browser_tab_list" in tool_name:
                        test_tool_name = tool_name
                        break
                
                if not test_tool_name and browser_tools:
                    test_tool_name = browser_tools[0]
                
                if test_tool_name:
                    print(f"🔧 测试工具: {test_tool_name}")
                    
                    # 获取工具实例
                    tool = agent.mcp_clients.tool_map[test_tool_name]
                    
                    # 执行工具
                    try:
                        result = await tool.execute()
                        print(f"✅ 工具执行完成: {result.output[:100] if result.output else 'No output'}...")
                    except Exception as e:
                        print(f"⚠️ 工具执行出错: {e}")
                        # 这是预期的，因为我们没有传递必要的参数
                
                # 检查操作日志摘要
                try:
                    summary = agent.mcp_clients.get_operation_log_summary()
                    print(f"📊 操作日志摘要: {summary}")
                except Exception as e:
                    print(f"⚠️ 获取操作日志摘要失败: {e}")
            else:
                print("❌ 没有找到浏览器工具")
        else:
            print("❌ 没有连接到 MCP 服务")

        # 保存日志并清理
        print("💾 保存操作日志...")
        try:
            log_file = await agent.mcp_clients.save_operation_logs()
            if log_file:
                print(f"✅ 操作日志已保存到: {log_file}")
            else:
                print("⚠️ 没有生成日志文件")
        except Exception as e:
            print(f"❌ 保存操作日志失败: {e}")

        # 清理
        await agent.cleanup()
        print("✅ 清理完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_operation_logging())
