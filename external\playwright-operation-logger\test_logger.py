#!/usr/bin/env python3
"""
Test script for Playwright Operation Logger

This script tests the operation logger functionality without requiring
a full MCP server setup.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add src directory to path
script_dir = Path(__file__).parent
src_dir = script_dir / "src"
sys.path.insert(0, str(src_dir))

from locator_extractor import LocatorExtractor
from log_formatter import LogFormatter
from mcp_interceptor import MCPToolInterceptor
from operation_logger import OperationLogger
from utils import setup_logging, validate_log_file

from config import LoggerConfig


async def test_operation_logger():
    """Test the core operation logger"""
    print("Testing OperationLogger...")

    # Create test logger
    logger = OperationLogger(
        log_dir="external/playwright-operation-logger/logs",
        session_prefix="test_session",
    )

    # Test various operations
    test_operations = [
        {
            "tool_name": "browser_navigate",
            "tool_args": {"url": "https://example.com"},
            "result": "Navigation completed successfully",
            "execution_time": 1500,
            "success": True,
        },
        {
            "tool_name": "browser_click",
            "tool_args": {"ref": "e2", "element": 'button "Submit"'},
            "result": "```js\nawait page.getByRole('button', { name: 'Submit' }).click();\n```",
            "execution_time": 500,
            "success": True,
        },
        {
            "tool_name": "browser_type",
            "tool_args": {
                "ref": "e3",
                "element": 'textbox "Username"',
                "text": "testuser",
            },
            "result": "```js\nawait page.getByRole('textbox', { name: 'Username' }).fill('testuser');\n```",
            "execution_time": 300,
            "success": True,
        },
        {
            "tool_name": "browser_select_option",
            "tool_args": {
                "ref": "e4",
                "element": 'combobox "Country"',
                "values": ["US"],
            },
            "result": "```js\nawait page.getByRole('combobox').selectOption('US');\n```",
            "execution_time": 400,
            "success": True,
        },
        {
            "tool_name": "browser_click",
            "tool_args": {"ref": "e5", "element": 'button "Invalid"'},
            "result": "Error: Element not found",
            "execution_time": 2000,
            "success": False,
        },
    ]

    for operation in test_operations:
        await logger.log_operation(**operation)

    # Wait a bit for async operations to complete
    await asyncio.sleep(0.5)

    # Save session
    filepath = await logger.save_session()
    print(f"Test session saved to: {filepath}")

    # Get summary
    summary = logger.get_session_summary()
    print(f"Session summary: {summary}")

    return filepath


async def test_locator_extractor():
    """Test the locator extractor"""
    print("\nTesting LocatorExtractor...")

    extractor = LocatorExtractor()

    test_cases = [
        {
            "tool_name": "browser_click",
            "tool_args": {"ref": "e2", "element": 'button "Submit"'},
            "result": "```js\nawait page.getByRole('button', { name: 'Submit' }).click();\n```",
        },
        {
            "tool_name": "browser_type",
            "tool_args": {
                "ref": "e3",
                "element": 'textbox "Username"',
                "text": "testuser",
            },
            "result": "```js\nawait page.getByRole('textbox', { name: 'Username' }).fill('testuser');\n```",
        },
        {
            "tool_name": "browser_navigate",
            "tool_args": {"url": "https://example.com"},
            "result": "Navigation completed",
        },
    ]

    for case in test_cases:
        locators = await extractor.extract_locators(
            case["tool_name"], case["tool_args"], case["result"]
        )
        print(f"Tool: {case['tool_name']}")
        print(f"  Extracted locators: {locators}")

        info = extractor.get_locator_info(case["tool_name"], case["tool_args"])
        print(f"  Locator info: {info}")
        print()


def test_log_formatter():
    """Test the log formatter"""
    print("Testing LogFormatter...")

    formatter = LogFormatter()

    test_cases = [
        {
            "tool_name": "browser_click",
            "tool_args": {"ref": "e2", "element": 'button "Submit"'},
            "result": "Click successful",
            "real_locators": {
                "role_selector": "getByRole('button', { name: 'Submit' })"
            },
        },
        {
            "tool_name": "browser_type",
            "tool_args": {
                "ref": "e3",
                "element": 'textbox "Username"',
                "text": "testuser",
            },
            "result": "Type successful",
            "real_locators": {
                "role_selector": "getByRole('textbox', { name: 'Username' })"
            },
        },
        {
            "tool_name": "browser_navigate",
            "tool_args": {"url": "https://example.com"},
            "result": "Navigation successful",
            "real_locators": None,
        },
    ]

    for case in test_cases:
        action_data = formatter.format_action(
            case["tool_name"], case["tool_args"], case["result"], case["real_locators"]
        )
        print(f"Tool: {case['tool_name']}")
        print(f"  Formatted action: {json.dumps(action_data, indent=2)}")
        print()


async def test_mcp_interceptor():
    """Test the MCP interceptor"""
    print("Testing MCPToolInterceptor...")

    # Create test logger
    logger = OperationLogger(
        log_dir="external/playwright-operation-logger/logs",
        session_prefix="interceptor_test",
    )

    interceptor = MCPToolInterceptor(logger)

    # Test logging hook
    logging_hook = interceptor.create_logging_hook()

    await logging_hook(
        tool_name="browser_click",
        tool_args={"ref": "e2", "element": 'button "Test"'},
        result="Click successful",
        execution_time=500,
        success=True,
    )

    # Wait a bit for async operations to complete
    await asyncio.sleep(0.5)

    # Get summary
    summary = interceptor.get_session_summary()
    print(f"Interceptor session summary: {summary}")

    # Save logs
    filepath = await interceptor.save_session_logs()
    print(f"Interceptor logs saved to: {filepath}")

    return filepath


def test_config():
    """Test configuration"""
    print("Testing Configuration...")

    # Test default config
    config = LoggerConfig()
    print(f"Default config: {config.to_dict()}")

    # Test config from environment
    os.environ["PLAYWRIGHT_LOGGER_ENABLED"] = "false"
    os.environ["PLAYWRIGHT_LOGGER_PREFIX"] = "env_test"

    env_config = LoggerConfig.from_env()
    print(f"Environment config: {env_config.to_dict()}")

    # Clean up environment
    del os.environ["PLAYWRIGHT_LOGGER_ENABLED"]
    del os.environ["PLAYWRIGHT_LOGGER_PREFIX"]


def validate_test_results(filepaths):
    """Validate test log files"""
    print("\nValidating test results...")

    for filepath in filepaths:
        if filepath and os.path.exists(filepath):
            result = validate_log_file(filepath)
            print(
                f"Validation for {os.path.basename(filepath)}: {'PASSED' if result['valid'] else 'FAILED'}"
            )

            if result["errors"]:
                print("  Errors:")
                for error in result["errors"]:
                    print(f"    - {error}")

            if result["warnings"]:
                print("  Warnings:")
                for warning in result["warnings"]:
                    print(f"    - {warning}")

            if result["info"]:
                print(f"  Records: {result['info'].get('total_records', 0)}")
                print(f"  File size: {result['info'].get('file_size_mb', 0)} MB")
        else:
            print(f"File not found: {filepath}")


async def main():
    """Main test function"""
    print("Playwright Operation Logger Test Suite")
    print("=" * 50)

    setup_logging("INFO")

    try:
        # Test individual components
        test_config()
        test_log_formatter()
        await test_locator_extractor()

        # Test integrated functionality
        filepath1 = await test_operation_logger()
        filepath2 = await test_mcp_interceptor()

        # Validate results
        validate_test_results([filepath1, filepath2])

        print("\n" + "=" * 50)
        print("All tests completed successfully!")

    except Exception as e:
        print(f"Test failed: {e}")
        import traceback

        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
