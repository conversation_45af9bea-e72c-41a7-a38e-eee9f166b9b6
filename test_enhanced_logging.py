#!/usr/bin/env python
"""
测试增强的操作日志记录系统
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def test_enhanced_logging():
    """测试增强的日志记录功能"""
    try:
        print("🚀 开始测试增强的操作日志记录系统...")
        
        # 创建 Manus 实例
        agent = await Manus.create()
        print("✅ Manus 实例创建成功")

        # 检查连接状态
        if agent.mcp_clients.sessions:
            available_tools = list(agent.mcp_clients.tool_map.keys())
            browser_tools = [tool for tool in available_tools if "browser" in tool.lower()]
            
            print(f"✅ 成功连接到 MCP 服务，发现 {len(browser_tools)} 个浏览器工具")
            
            # 查找需要的工具
            tab_list_tool = None
            for tool_name in browser_tools:
                if "browser_tab_list" in tool_name:
                    tab_list_tool = agent.mcp_clients.tool_map[tool_name]
                    break
            
            if tab_list_tool:
                print("🔧 测试工具: browser_tab_list")
                
                # 执行工具
                try:
                    result = await tab_list_tool.execute()
                    print(f"✅ 工具执行完成")
                except Exception as e:
                    print(f"⚠️ 工具执行出错: {e}")
                
                # 等待一下，然后再执行一次来测试时间窗口
                await asyncio.sleep(1)
                
                try:
                    result2 = await tab_list_tool.execute()
                    print(f"✅ 第二次工具执行完成")
                except Exception as e:
                    print(f"⚠️ 第二次工具执行出错: {e}")
            else:
                print("❌ 没有找到 browser_tab_list 工具")
        else:
            print("❌ 没有连接到 MCP 服务")

        # 检查操作日志摘要
        try:
            summary = agent.mcp_clients.get_operation_log_summary()
            print(f"📊 操作日志摘要: {summary}")
        except Exception as e:
            print(f"⚠️ 获取操作日志摘要失败: {e}")

        # 保存日志并清理
        print("💾 保存操作日志...")
        try:
            log_file = await agent.mcp_clients.save_operation_logs()
            if log_file:
                print(f"✅ 操作日志已保存到: {log_file}")
                
                # 读取并显示日志内容
                try:
                    import json
                    with open(log_file, 'r', encoding='utf-8') as f:
                        log_data = json.load(f)
                    
                    print("\n📄 日志内容预览:")
                    print(f"会话ID: {log_data.get('session_id')}")
                    print(f"操作记录数: {len(log_data.get('records', []))}")
                    
                    for i, record in enumerate(log_data.get('records', []), 1):
                        print(f"\n操作 {i}:")
                        print(f"  - 步骤: {record.get('step')}")
                        print(f"  - 成功: {record.get('success')}")
                        print(f"  - 原始动作: {record.get('original_action')}")
                        print(f"  - 分类: {record.get('category')}")
                        print(f"  - 动作数量: {len(record.get('actions', []))}")
                        
                        # 显示定位器信息
                        real_locators = record.get('real_locators')
                        if real_locators:
                            print(f"  - 真实定位器: {real_locators}")
                        
                        # 显示动作详情
                        for j, action in enumerate(record.get('actions', []), 1):
                            action_name = list(action.keys())[0] if action else "unknown"
                            print(f"    动作 {j}: {action_name}")
                            
                            # 检查结果中的定位器
                            results = action.get(action_name, {}).get('results', {})
                            if 'role_selector' in results:
                                print(f"      定位器: {results['role_selector']}")
                
                except Exception as e:
                    print(f"⚠️ 读取日志文件失败: {e}")
            else:
                print("⚠️ 没有生成日志文件")
        except Exception as e:
            print(f"❌ 保存操作日志失败: {e}")

        # 清理
        await agent.cleanup()
        print("✅ 清理完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_enhanced_logging())
