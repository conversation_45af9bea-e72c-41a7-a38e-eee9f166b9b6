#!/usr/bin/env python
"""
单元测试定位器转换逻辑
"""
import sys
import os
from pathlib import Path

# 添加 playwright-operation-logger 到路径
logger_path = Path(__file__).resolve().parent / "external" / "playwright-operation-logger" / "src"
sys.path.insert(0, str(logger_path))

from locator_extractor import LocatorExtractor


def test_locator_conversion():
    """测试定位器转换"""
    print("🧪 测试定位器转换逻辑...")
    
    extractor = LocatorExtractor()
    
    # 测试用例
    test_cases = [
        {
            "input": 'getByRole("searchbox", { name: "输入搜索词" })',
            "expected": 'get_by_role("searchbox", name="输入搜索词")',
            "description": "getByRole with name parameter"
        },
        {
            "input": 'getByRole("button")',
            "expected": 'get_by_role("button")',
            "description": "getByRole without name parameter"
        },
        {
            "input": 'getByText("Click me")',
            "expected": 'get_by_text("Click me")',
            "description": "getByText"
        },
        {
            "input": 'getByPlaceholder("Enter your name")',
            "expected": 'get_by_placeholder("Enter your name")',
            "description": "getByPlaceholder"
        },
        {
            "input": 'getByLabel("Username")',
            "expected": 'get_by_label("Username")',
            "description": "getByLabel"
        }
    ]
    
    print("\n📋 测试结果:")
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        input_locator = test_case["input"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        # 调用转换方法
        result = extractor._format_locator_string(input_locator)
        
        # 检查结果
        if result == expected:
            print(f"✅ 测试 {i}: {description}")
            print(f"   输入: {input_locator}")
            print(f"   输出: {result}")
        else:
            print(f"❌ 测试 {i}: {description}")
            print(f"   输入: {input_locator}")
            print(f"   期望: {expected}")
            print(f"   实际: {result}")
            all_passed = False
        print()
    
    if all_passed:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
    
    return all_passed


def test_camel_to_snake():
    """测试驼峰转下划线"""
    print("🧪 测试驼峰转下划线...")
    
    extractor = LocatorExtractor()
    
    test_cases = [
        ("getByRole", "get_by_role"),
        ("getByText", "get_by_text"),
        ("getByPlaceholder", "get_by_placeholder"),
        ("getByLabel", "get_by_label"),
        ("getByTestId", "get_by_test_id"),
        ("getByAltText", "get_by_alt_text"),
        ("getByTitle", "get_by_title"),
    ]
    
    print("\n📋 测试结果:")
    all_passed = True
    
    for i, (input_str, expected) in enumerate(test_cases, 1):
        result = extractor._camel_to_snake(input_str)
        
        if result == expected:
            print(f"✅ 测试 {i}: {input_str} -> {result}")
        else:
            print(f"❌ 测试 {i}: {input_str}")
            print(f"   期望: {expected}")
            print(f"   实际: {result}")
            all_passed = False
    
    if all_passed:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
    
    return all_passed


if __name__ == "__main__":
    print("🚀 开始单元测试...")
    
    test1_passed = test_camel_to_snake()
    print("\n" + "="*50 + "\n")
    test2_passed = test_locator_conversion()
    
    if test1_passed and test2_passed:
        print("\n🎉 所有单元测试通过！")
    else:
        print("\n⚠️ 部分单元测试失败")
