#!/usr/bin/env python
"""
测试定位器转换和复合操作记录
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).resolve().parent
sys.path.insert(0, str(project_root))

from app.agent.manus import Manus
from app.logger import logger


async def test_search_operation():
    """测试搜索操作的完整记录"""
    try:
        print("🚀 开始测试搜索操作记录...")
        
        # 创建 Manus 实例
        agent = await Manus.create()
        print("✅ Manus 实例创建成功")

        # 检查连接状态
        if agent.mcp_clients.sessions:
            available_tools = list(agent.mcp_clients.tool_map.keys())
            
            # 查找需要的工具
            tab_new_tool = None
            type_tool = None
            press_key_tool = None
            
            for tool_name in available_tools:
                if "browser_tab_new" in tool_name:
                    tab_new_tool = agent.mcp_clients.tool_map[tool_name]
                elif "browser_type" in tool_name:
                    type_tool = agent.mcp_clients.tool_map[tool_name]
                elif "browser_press_key" in tool_name:
                    press_key_tool = agent.mcp_clients.tool_map[tool_name]
            
            if tab_new_tool and type_tool and press_key_tool:
                print("✅ 找到所需的工具")
                
                # 1. 打开新标签页到搜索引擎
                print("🔧 步骤1: 打开新标签页")
                try:
                    result1 = await tab_new_tool.execute(url="https://www.bing.com")
                    print(f"✅ 新标签页打开成功")
                except Exception as e:
                    print(f"⚠️ 打开新标签页失败: {e}")
                
                # 等待页面加载
                await asyncio.sleep(2)
                
                # 2. 在搜索框输入文本
                print("🔧 步骤2: 在搜索框输入文本")
                try:
                    # 这里需要实际的搜索框引用，通常从页面快照中获取
                    result2 = await type_tool.execute(
                        ref="e31",  # 假设的搜索框引用
                        text="重庆天气"
                    )
                    print(f"✅ 文本输入成功")
                except Exception as e:
                    print(f"⚠️ 文本输入失败: {e}")
                
                # 3. 按回车键搜索
                print("🔧 步骤3: 按回车键搜索")
                try:
                    result3 = await press_key_tool.execute(
                        ref="e31",  # 同一个搜索框
                        key="Enter"
                    )
                    print(f"✅ 搜索执行成功")
                except Exception as e:
                    print(f"⚠️ 搜索执行失败: {e}")
                
            else:
                print("❌ 缺少必要的工具")
                print(f"tab_new_tool: {tab_new_tool is not None}")
                print(f"type_tool: {type_tool is not None}")
                print(f"press_key_tool: {press_key_tool is not None}")
        else:
            print("❌ 没有连接到 MCP 服务")

        # 检查操作日志摘要
        try:
            summary = agent.mcp_clients.get_operation_log_summary()
            print(f"📊 操作日志摘要: {summary}")
        except Exception as e:
            print(f"⚠️ 获取操作日志摘要失败: {e}")

        # 保存日志并清理
        print("💾 保存操作日志...")
        try:
            log_file = await agent.mcp_clients.save_operation_logs()
            if log_file:
                print(f"✅ 操作日志已保存到: {log_file}")
            else:
                print("⚠️ 没有生成日志文件")
        except Exception as e:
            print(f"❌ 保存操作日志失败: {e}")

        # 清理
        await agent.cleanup()
        print("✅ 清理完成")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_search_operation())
