"""
Utility functions for Playwright Operation Logger
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


def setup_logging(log_level: str = "INFO") -> None:
    """Setup logging for the operation logger"""
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def ensure_log_directory(log_dir: str) -> Path:
    """Ensure log directory exists and return Path object"""
    path = Path(log_dir)
    path.mkdir(parents=True, exist_ok=True)
    return path


def cleanup_old_logs(log_dir: str, max_files: int = 50) -> int:
    """
    Clean up old log files, keeping only the most recent ones.
    
    Args:
        log_dir: Directory containing log files
        max_files: Maximum number of files to keep
        
    Returns:
        Number of files deleted
    """
    try:
        log_path = Path(log_dir)
        if not log_path.exists():
            return 0
        
        # Get all JSON log files
        log_files = list(log_path.glob("*.json"))
        
        if len(log_files) <= max_files:
            return 0
        
        # Sort by modification time (newest first)
        log_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
        
        # Delete oldest files
        files_to_delete = log_files[max_files:]
        deleted_count = 0
        
        for file_path in files_to_delete:
            try:
                file_path.unlink()
                deleted_count += 1
                logger.debug(f"Deleted old log file: {file_path}")
            except Exception as e:
                logger.error(f"Failed to delete {file_path}: {e}")
        
        logger.info(f"Cleaned up {deleted_count} old log files")
        return deleted_count
        
    except Exception as e:
        logger.error(f"Error during log cleanup: {e}")
        return 0


def get_log_file_info(log_dir: str) -> List[Dict[str, Any]]:
    """
    Get information about existing log files.
    
    Args:
        log_dir: Directory containing log files
        
    Returns:
        List of file information dictionaries
    """
    try:
        log_path = Path(log_dir)
        if not log_path.exists():
            return []
        
        log_files = []
        for file_path in log_path.glob("*.json"):
            try:
                stat = file_path.stat()
                
                # Try to read session info from file
                session_info = {}
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        session_info = {
                            'session_id': data.get('session_id', 'unknown'),
                            'start_time': data.get('start_time', 0),
                            'total_operations': len(data.get('records', [])),
                            'metadata': data.get('metadata', {})
                        }
                except Exception:
                    # If we can't read the file, just use basic info
                    pass
                
                log_files.append({
                    'filename': file_path.name,
                    'path': str(file_path),
                    'size_bytes': stat.st_size,
                    'size_mb': round(stat.st_size / (1024 * 1024), 2),
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    **session_info
                })
                
            except Exception as e:
                logger.error(f"Error reading file info for {file_path}: {e}")
        
        # Sort by modification time (newest first)
        log_files.sort(key=lambda f: f['modified'], reverse=True)
        return log_files
        
    except Exception as e:
        logger.error(f"Error getting log file info: {e}")
        return []


def validate_log_file(file_path: str) -> Dict[str, Any]:
    """
    Validate a log file and return validation results.
    
    Args:
        file_path: Path to the log file
        
    Returns:
        Validation results dictionary
    """
    result = {
        'valid': False,
        'errors': [],
        'warnings': [],
        'info': {}
    }
    
    try:
        path = Path(file_path)
        if not path.exists():
            result['errors'].append(f"File does not exist: {file_path}")
            return result
        
        # Try to parse JSON
        with open(path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check required fields
        required_fields = ['session_id', 'start_time', 'records', 'metadata']
        for field in required_fields:
            if field not in data:
                result['errors'].append(f"Missing required field: {field}")
        
        # Validate records structure
        records = data.get('records', [])
        if not isinstance(records, list):
            result['errors'].append("Records field must be a list")
        else:
            for i, record in enumerate(records):
                if not isinstance(record, dict):
                    result['errors'].append(f"Record {i} is not a dictionary")
                    continue
                
                # Check required record fields
                record_fields = ['step', 'success', 'actions', 'timestamp']
                for field in record_fields:
                    if field not in record:
                        result['warnings'].append(f"Record {i} missing field: {field}")
        
        # Collect info
        result['info'] = {
            'session_id': data.get('session_id'),
            'total_records': len(records),
            'file_size_mb': round(path.stat().st_size / (1024 * 1024), 2),
            'metadata': data.get('metadata', {})
        }
        
        # If no errors, mark as valid
        if not result['errors']:
            result['valid'] = True
        
    except json.JSONDecodeError as e:
        result['errors'].append(f"Invalid JSON: {e}")
    except Exception as e:
        result['errors'].append(f"Error validating file: {e}")
    
    return result


def merge_log_files(file_paths: List[str], output_path: str) -> bool:
    """
    Merge multiple log files into a single file.
    
    Args:
        file_paths: List of log file paths to merge
        output_path: Output file path
        
    Returns:
        True if successful, False otherwise
    """
    try:
        merged_records = []
        merged_metadata = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'enhanced_logging': True,
            'merged_from': []
        }
        
        for file_path in file_paths:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                records = data.get('records', [])
                merged_records.extend(records)
                
                # Update metadata
                metadata = data.get('metadata', {})
                merged_metadata['total_operations'] += metadata.get('total_operations', 0)
                merged_metadata['successful_operations'] += metadata.get('successful_operations', 0)
                merged_metadata['failed_operations'] += metadata.get('failed_operations', 0)
                merged_metadata['merged_from'].append({
                    'file': Path(file_path).name,
                    'session_id': data.get('session_id'),
                    'records_count': len(records)
                })
                
            except Exception as e:
                logger.error(f"Error reading {file_path}: {e}")
                continue
        
        # Sort records by timestamp
        merged_records.sort(key=lambda r: r.get('timestamp', 0))
        
        # Renumber steps
        for i, record in enumerate(merged_records, 1):
            record['step'] = i
        
        # Create merged data
        merged_data = {
            'session_id': f"merged_{int(datetime.now().timestamp())}",
            'start_time': merged_records[0]['timestamp'] if merged_records else int(datetime.now().timestamp() * 1000),
            'records': merged_records,
            'metadata': merged_metadata
        }
        
        # Save merged file
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(merged_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Merged {len(file_paths)} files into {output_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error merging log files: {e}")
        return False
