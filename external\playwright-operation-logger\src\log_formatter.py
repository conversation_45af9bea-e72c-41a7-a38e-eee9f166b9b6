"""
Log Formatter - Format operation data for JSON logging
"""

import logging
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)


class LogFormatter:
    """
    Format operation data into the expected JSON log structure.

    This class handles formatting of different operation types to match
    the existing log format while adding enhanced locator information.
    """

    def __init__(self):
        pass

    def format_action(
        self,
        tool_name: str,
        tool_args: Dict[str, Any],
        result: Any,
        real_locators: Optional[Dict[str, str]] = None,
    ) -> Dict[str, Any]:
        """
        Format action data for logging.

        Args:
            tool_name: Name of the MCP tool
            tool_args: Arguments passed to the tool
            result: Tool execution result
            real_locators: Extracted real locators

        Returns:
            Formatted action dictionary
        """
        try:
            # Base action structure
            action_data = {
                tool_name: self._format_tool_specific_data(tool_name, tool_args),
                "results": self._format_results(result, real_locators),
            }

            return action_data

        except Exception as e:
            logger.error(f"Error formatting action for {tool_name}: {e}")
            return {tool_name: tool_args, "results": {"error": str(e)}}

    def _format_tool_specific_data(
        self, tool_name: str, tool_args: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Format tool-specific data based on operation type"""
        try:
            if tool_name == "browser_navigate":
                return {"url": tool_args.get("url"), "needs_url_extraction": True}

            elif tool_name == "browser_tab_new":
                return {
                    "needs_url_extraction": True,
                    "target_url": tool_args.get("url"),
                }

            elif tool_name == "browser_click":
                return {
                    "ref": tool_args.get("ref"),
                    "role_selector": self._construct_role_selector(tool_args),
                    "element_description": tool_args.get("element"),
                }

            elif tool_name == "browser_type":
                return {
                    "ref": tool_args.get("ref"),
                    "text": tool_args.get("text"),
                    "role_selector": self._construct_role_selector(tool_args),
                    "element_description": tool_args.get("element"),
                }

            elif tool_name == "browser_select_option":
                return {
                    "ref": tool_args.get("ref"),
                    "values": tool_args.get("values"),
                    "role_selector": self._construct_role_selector(tool_args),
                    "element_description": tool_args.get("element"),
                }

            elif tool_name == "browser_file_upload":
                return {
                    "ref": tool_args.get("ref"),
                    "file_path": tool_args.get("file_path"),
                    "role_selector": self._construct_role_selector(tool_args),
                    "element_description": tool_args.get("element"),
                }

            elif tool_name.startswith("browser_wait"):
                wait_data = {"condition": tool_name.replace("browser_wait_for_", "")}
                if "text" in tool_args:
                    wait_data["text"] = tool_args["text"]
                if "timeout" in tool_args:
                    wait_data["timeout"] = tool_args["timeout"]
                return wait_data

            else:
                # Generic formatting for other tools
                return tool_args.copy()

        except Exception as e:
            logger.error(f"Error formatting tool-specific data: {e}")
            return tool_args.copy()

    def _construct_role_selector(self, tool_args: Dict[str, Any]) -> Optional[str]:
        """Construct a role selector from tool arguments"""
        try:
            element_desc = tool_args.get("element", "")
            if not element_desc:
                return None

            # Try to extract role and name from element description
            element_lower = element_desc.lower()

            if "button" in element_lower:
                name = self._extract_quoted_text(element_desc)
                if name:
                    return f"role=button name={name}"
                else:
                    return "role=button"

            elif "textbox" in element_lower:
                name = self._extract_quoted_text(element_desc)
                if name:
                    return f"role=textbox name={name}"
                else:
                    return "role=textbox"

            elif "link" in element_lower:
                name = self._extract_quoted_text(element_desc)
                if name:
                    return f"role=link name={name}"
                else:
                    return "role=link"

            elif "checkbox" in element_lower:
                name = self._extract_quoted_text(element_desc)
                if name:
                    return f"role=checkbox name={name}"
                else:
                    return "role=checkbox"

            elif "combobox" in element_lower or "select" in element_lower:
                return "role=combobox"

            else:
                # Generic text selector
                return f"text={element_desc}"

        except Exception as e:
            logger.error(f"Error constructing role selector: {e}")
            return None

    def _extract_quoted_text(self, text: str) -> Optional[str]:
        """Extract text within quotes"""
        import re

        try:
            # Look for text in double quotes
            matches = re.findall(r'"([^"]+)"', text)
            if matches:
                return matches[0]

            # Look for text in single quotes
            matches = re.findall(r"'([^']+)'", text)
            if matches:
                return matches[0]

            return None

        except Exception as e:
            logger.error(f"Error extracting quoted text: {e}")
            return None

    def _format_results(
        self, result: Any, real_locators: Optional[Dict[str, str]] = None
    ) -> Dict[str, Any]:
        """Format tool execution results"""
        try:
            result_data = {
                "include_in_memory": True,
                "execution_time": 0,  # Will be updated by the logger
            }

            # Add real locator information if available
            if real_locators and "role_selector" in real_locators:
                result_data["role_selector"] = real_locators["role_selector"]

            return result_data

        except Exception as e:
            logger.error(f"Error formatting results: {e}")
            return {
                "include_in_memory": False,
                "execution_time": 0,
                "error": f"Error formatting results: {e}",
            }

    def format_session_summary(
        self,
        session_id: str,
        start_time: int,
        operations: int,
        successful: int,
        failed: int,
    ) -> Dict[str, Any]:
        """Format session summary metadata"""
        return {
            "session_id": session_id,
            "start_time": start_time,
            "metadata": {
                "total_operations": operations,
                "successful_operations": successful,
                "failed_operations": failed,
                "enhanced_logging": True,
            },
        }
