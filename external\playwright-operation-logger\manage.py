#!/usr/bin/env python3
"""
Playwright Operation Logger Management Script

This script provides command-line interface for managing the operation logger.
"""

import argparse
import asyncio
import json
import sys
import os
from pathlib import Path

# Add src directory to path
script_dir = Path(__file__).parent
src_dir = script_dir / "src"
sys.path.insert(0, str(src_dir))

from config import LoggerConfig, get_config
from operation_logger import OperationLogger
from mcp_interceptor import get_or_create_interceptor
from utils import setup_logging, get_log_file_info, cleanup_old_logs, validate_log_file


async def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Manage Playwright Operation Logger")
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show current status")
    
    # Enable command
    enable_parser = subparsers.add_parser("enable", help="Enable operation logging")
    
    # Disable command
    disable_parser = subparsers.add_parser("disable", help="Disable operation logging")
    
    # Config command
    config_parser = subparsers.add_parser("config", help="Show current configuration")
    config_parser.add_argument("--set", nargs=2, metavar=("KEY", "VALUE"), 
                              help="Set configuration value")
    
    # Logs command
    logs_parser = subparsers.add_parser("logs", help="Manage log files")
    logs_parser.add_argument("--list", action="store_true", help="List log files")
    logs_parser.add_argument("--cleanup", action="store_true", help="Clean up old logs")
    logs_parser.add_argument("--validate", metavar="FILE", help="Validate log file")
    logs_parser.add_argument("--max-files", type=int, default=50, 
                           help="Maximum files to keep during cleanup")
    
    # Save command
    save_parser = subparsers.add_parser("save", help="Save current session logs")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Test operation logger")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    setup_logging()
    
    try:
        if args.command == "status":
            await show_status()
        elif args.command == "enable":
            await enable_logging()
        elif args.command == "disable":
            await disable_logging()
        elif args.command == "config":
            await manage_config(args)
        elif args.command == "logs":
            await manage_logs(args)
        elif args.command == "save":
            await save_session()
        elif args.command == "test":
            await test_logger()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


async def show_status():
    """Show current status"""
    print("Playwright Operation Logger Status")
    print("=" * 40)
    
    config = get_config()
    print(f"Enabled: {config.enabled}")
    print(f"Log Directory: {config.log_dir}")
    print(f"Session Prefix: {config.session_prefix}")
    print(f"Async Logging: {config.async_logging}")
    print(f"Extract Real Locators: {config.extract_real_locators}")
    
    # Check if interceptor is available
    try:
        interceptor = get_or_create_interceptor()
        summary = interceptor.get_session_summary()
        print(f"\nCurrent Session: {summary.get('session_id', 'N/A')}")
        print(f"Total Operations: {summary.get('total_operations', 0)}")
        print(f"Successful: {summary.get('successful_operations', 0)}")
        print(f"Failed: {summary.get('failed_operations', 0)}")
    except Exception as e:
        print(f"\nInterceptor Status: Error - {e}")
    
    # Log directory info
    if os.path.exists(config.log_dir):
        log_files = get_log_file_info(config.log_dir)
        print(f"\nLog Files: {len(log_files)} files")
        if log_files:
            total_size = sum(f['size_mb'] for f in log_files)
            print(f"Total Size: {total_size:.2f} MB")
    else:
        print(f"\nLog Directory: Not found ({config.log_dir})")


async def enable_logging():
    """Enable operation logging"""
    try:
        interceptor = get_or_create_interceptor()
        interceptor.enable()
        print("Operation logging enabled")
    except Exception as e:
        print(f"Failed to enable logging: {e}")


async def disable_logging():
    """Disable operation logging"""
    try:
        interceptor = get_or_create_interceptor()
        interceptor.disable()
        print("Operation logging disabled")
    except Exception as e:
        print(f"Failed to disable logging: {e}")


async def manage_config(args):
    """Manage configuration"""
    config_file = script_dir / "config.json"
    
    if args.set:
        key, value = args.set
        
        # Load current config
        if config_file.exists():
            with open(config_file, 'r') as f:
                config_data = json.load(f)
        else:
            config_data = {}
        
        # Convert value to appropriate type
        if value.lower() in ('true', 'false'):
            value = value.lower() == 'true'
        elif value.isdigit():
            value = int(value)
        elif value.replace('.', '').isdigit():
            value = float(value)
        
        config_data[key] = value
        
        # Save config
        with open(config_file, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"Configuration updated: {key} = {value}")
    else:
        # Show current config
        config = get_config()
        print("Current Configuration:")
        print("=" * 30)
        config_dict = config.to_dict()
        for key, value in config_dict.items():
            print(f"{key}: {value}")


async def manage_logs(args):
    """Manage log files"""
    config = get_config()
    
    if args.list:
        log_files = get_log_file_info(config.log_dir)
        if not log_files:
            print("No log files found")
            return
        
        print(f"Log Files in {config.log_dir}:")
        print("=" * 50)
        for file_info in log_files:
            print(f"File: {file_info['filename']}")
            print(f"  Size: {file_info['size_mb']} MB")
            print(f"  Modified: {file_info['modified']}")
            if 'session_id' in file_info:
                print(f"  Session: {file_info['session_id']}")
                print(f"  Operations: {file_info.get('total_operations', 'N/A')}")
            print()
    
    elif args.cleanup:
        deleted = cleanup_old_logs(config.log_dir, args.max_files)
        print(f"Cleaned up {deleted} old log files")
    
    elif args.validate:
        result = validate_log_file(args.validate)
        print(f"Validation Results for {args.validate}:")
        print("=" * 40)
        print(f"Valid: {result['valid']}")
        
        if result['errors']:
            print("Errors:")
            for error in result['errors']:
                print(f"  - {error}")
        
        if result['warnings']:
            print("Warnings:")
            for warning in result['warnings']:
                print(f"  - {warning}")
        
        if result['info']:
            print("Info:")
            for key, value in result['info'].items():
                print(f"  {key}: {value}")


async def save_session():
    """Save current session logs"""
    try:
        interceptor = get_or_create_interceptor()
        filepath = await interceptor.save_session_logs()
        if filepath:
            print(f"Session logs saved to: {filepath}")
        else:
            print("No logs to save or save failed")
    except Exception as e:
        print(f"Failed to save session: {e}")


async def test_logger():
    """Test the operation logger"""
    print("Testing Operation Logger...")
    
    try:
        # Create test logger
        logger = OperationLogger(
            log_dir="external/playwright-operation-logger/logs",
            session_prefix="test_session"
        )
        
        # Test logging a few operations
        await logger.log_operation(
            tool_name="browser_navigate",
            tool_args={"url": "https://example.com"},
            result="Navigation successful",
            execution_time=1500,
            success=True
        )
        
        await logger.log_operation(
            tool_name="browser_click",
            tool_args={"ref": "e2", "element": "button \"Submit\""},
            result="Click successful",
            execution_time=500,
            success=True
        )
        
        # Save test session
        filepath = await logger.save_session()
        print(f"Test session saved to: {filepath}")
        
        # Validate the test file
        if filepath:
            result = validate_log_file(filepath)
            print(f"Test file validation: {'PASSED' if result['valid'] else 'FAILED'}")
            if result['errors']:
                for error in result['errors']:
                    print(f"  Error: {error}")
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
