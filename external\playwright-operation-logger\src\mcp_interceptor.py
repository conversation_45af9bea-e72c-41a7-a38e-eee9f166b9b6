"""
MCP Tool Interceptor - Intercept and log MCP tool calls
"""

import functools
import logging
import time
from typing import Any, Callable, Dict, Optional

try:
    from .operation_logger import OperationLogger
except ImportError:
    from operation_logger import OperationLogger

logger = logging.getLogger(__name__)


class MCPToolInterceptor:
    """
    Intercept MCP tool calls to enable operation logging.

    This class provides decorators and hooks to integrate with the existing
    MCP tool execution flow without modifying the core MCP implementation.
    """

    def __init__(self, operation_logger: OperationLogger):
        self.operation_logger = operation_logger
        self.enabled = True

    def enable(self):
        """Enable interception"""
        self.enabled = True
        logger.info("MCP tool interception enabled")

    def disable(self):
        """Disable interception"""
        self.enabled = False
        logger.info("MCP tool interception disabled")

    def intercept_tool_execution(self, original_execute_func: Callable) -> Callable:
        """
        Decorator to intercept tool execution.

        Args:
            original_execute_func: The original tool execute function

        Returns:
            Wrapped function with logging capability
        """

        @functools.wraps(original_execute_func)
        async def wrapper(tool_instance, **kwargs):
            if not self.enabled:
                return await original_execute_func(tool_instance, **kwargs)

            # Extract tool information
            tool_name = getattr(tool_instance, "original_name", "unknown_tool")
            start_time = time.time()

            try:
                # Execute the original tool
                result = await original_execute_func(tool_instance, **kwargs)

                # Calculate execution time
                execution_time = (
                    time.time() - start_time
                ) * 1000  # Convert to milliseconds

                # Log the operation
                await self.operation_logger.log_operation(
                    tool_name=tool_name,
                    tool_args=kwargs,
                    result=result,
                    execution_time=execution_time,
                    success=True,
                    page_info=None,  # Will be enhanced later if needed
                )

                return result

            except Exception as e:
                # Calculate execution time for failed operations
                execution_time = (time.time() - start_time) * 1000

                # Log the failed operation
                await self.operation_logger.log_operation(
                    tool_name=tool_name,
                    tool_args=kwargs,
                    result=f"Error: {str(e)}",
                    execution_time=execution_time,
                    success=False,
                    page_info=None,
                )

                # Re-raise the exception
                raise

        return wrapper

    def create_logging_hook(self) -> Callable:
        """
        Create a logging hook that can be called manually.

        Returns:
            Function that can be called to log operations
        """

        async def logging_hook(
            tool_name: str,
            tool_args: Dict[str, Any],
            result: Any,
            execution_time: float,
            success: bool = True,
            page_info: Optional[Dict[str, str]] = None,
        ):
            """
            Manual logging hook for integration points.

            Args:
                tool_name: Name of the tool
                tool_args: Tool arguments
                result: Execution result
                execution_time: Execution time in milliseconds
                success: Whether operation succeeded
                page_info: Optional page information
            """
            if not self.enabled:
                return

            await self.operation_logger.log_operation(
                tool_name=tool_name,
                tool_args=tool_args,
                result=result,
                execution_time=execution_time,
                success=success,
                page_info=page_info,
            )

        return logging_hook

    def patch_mcp_tool_class(self, mcp_tool_class):
        """
        Patch an MCP tool class to add logging.

        Args:
            mcp_tool_class: The MCPTool class to patch
        """
        if not hasattr(mcp_tool_class, "_original_execute"):
            # Store original execute method
            mcp_tool_class._original_execute = mcp_tool_class.execute

            # Replace with intercepted version
            mcp_tool_class.execute = self.intercept_tool_execution(
                mcp_tool_class._original_execute
            )

            logger.info(f"Patched {mcp_tool_class.__name__} for logging")

    def unpatch_mcp_tool_class(self, mcp_tool_class):
        """
        Remove logging patch from MCP tool class.

        Args:
            mcp_tool_class: The MCPTool class to unpatch
        """
        if hasattr(mcp_tool_class, "_original_execute"):
            # Restore original execute method
            mcp_tool_class.execute = mcp_tool_class._original_execute
            delattr(mcp_tool_class, "_original_execute")

            logger.info(f"Unpatched {mcp_tool_class.__name__}")

    async def save_session_logs(self) -> str:
        """
        Save current session logs.

        Returns:
            Path to saved log file
        """
        return await self.operation_logger.save_session()

    def get_session_summary(self) -> Dict[str, Any]:
        """
        Get current session summary.

        Returns:
            Session summary dictionary
        """
        return self.operation_logger.get_session_summary()


# Global interceptor instance (will be initialized when needed)
_global_interceptor: Optional[MCPToolInterceptor] = None


def get_global_interceptor() -> Optional[MCPToolInterceptor]:
    """Get the global interceptor instance"""
    return _global_interceptor


def initialize_global_interceptor(
    operation_logger: OperationLogger,
) -> MCPToolInterceptor:
    """Initialize the global interceptor"""
    global _global_interceptor
    _global_interceptor = MCPToolInterceptor(operation_logger)
    return _global_interceptor


def get_or_create_interceptor(
    log_dir: str = "external/playwright-operation-logger/logs",
) -> MCPToolInterceptor:
    """Get existing interceptor or create a new one"""
    global _global_interceptor

    if _global_interceptor is None:
        operation_logger = OperationLogger(log_dir=log_dir)
        _global_interceptor = MCPToolInterceptor(operation_logger)

    return _global_interceptor
